# Getting Started with Create React App

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

The page will reload when you make changes.

### `Dependencies used`

The project made use of the tensorflow javascript library for the object detection and Tailwind library for the styling

### `Architectural Process`

The project made use of simple React Js due to the 3 day timeframe but clean architectural code with reusable components built.

### `View Code`

You can view the project on the web using this link [https://assessment-app-1mqy.vercel.app/]

